import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/shared/models/booking/booking_model.dart';

@injectable
class BookingRepository {
  final FirebaseFirestore _firestore;

  BookingRepository(this._firestore);

  Future<List<BookingModel>> getBookings(String priestId) async {
    try {
      final snapshot = await _firestore
          .collection('bookings')
          .where('provider_id', isEqualTo: priestId)
          .where('type', isEqualTo: 0) // BookingType.priest = 0
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => BookingModel.fromJson({
        'id': doc.id,
        ...doc.data(),
      })).toList();
    } catch (e) {
      throw Exception('Failed to load bookings: $e');
    }
  }

  Future<void> confirmBooking(String bookingId) async {
    try {
      await _firestore.collection('bookings').doc(bookingId).update({
        'status': 1, // BookingStatus.confirmed = 1
        'updatedAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw Exception('Failed to confirm booking: $e');
    }
  }

  Future<void> cancelBooking(String bookingId) async {
    try {
      await _firestore.collection('bookings').doc(bookingId).update({
        'status': 4, // BookingStatus.cancelled = 4
        'updatedAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw Exception('Failed to cancel booking: $e');
    }
  }

  Future<void> completeBooking(String bookingId) async {
    try {
      await _firestore.collection('bookings').doc(bookingId).update({
        'status': 3, // BookingStatus.completed = 3
        'updatedAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw Exception('Failed to complete booking: $e');
    }
  }
}
