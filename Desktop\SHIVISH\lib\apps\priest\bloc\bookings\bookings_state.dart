import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/booking/booking_model.dart';

part 'bookings_state.freezed.dart';

@freezed
class BookingsState with _$BookingsState {
  const factory BookingsState.initial() = _Initial;
  const factory BookingsState.loading() = _Loading;
  const factory BookingsState.loaded(List<BookingModel> bookings) = _Loaded;
  const factory BookingsState.error(String message) = _Error;
}
