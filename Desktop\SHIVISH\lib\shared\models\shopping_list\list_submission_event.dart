import 'package:freezed_annotation/freezed_annotation.dart';

part 'list_submission_event.freezed.dart';
part 'list_submission_event.g.dart';

@freezed
class ListSubmissionEvent with _$ListSubmissionEvent {
  const factory ListSubmissionEvent.submitted({
    required String submissionId,
    required String buyerId,
    required String sellerId,
    required DateTime timestamp,
  }) = ListSubmissionEventSubmitted;

  const factory ListSubmissionEvent.quoted({
    required String submissionId,
    required String sellerId,
    required double amount,
    required DateTime timestamp,
  }) = ListSubmissionEventQuoted;

  const factory ListSubmissionEvent.counterOffered({
    required String submissionId,
    required String buyerId,
    required double amount,
    required DateTime timestamp,
  }) = ListSubmissionEventCounterOffered;

  const factory ListSubmissionEvent.accepted({
    required String submissionId,
    required String actorId,
    required double amount,
    required DateTime timestamp,
  }) = ListSubmissionEventAccepted;

  const factory ListSubmissionEvent.rejected({
    required String submissionId,
    required String actorId,
    required String reason,
    required DateTime timestamp,
  }) = ListSubmissionEventRejected;

  const factory ListSubmissionEvent.expired({
    required String submissionId,
    required DateTime timestamp,
  }) = ListSubmissionEventExpired;

  const factory ListSubmissionEvent.cancelled({
    required String submissionId,
    required String actorId,
    required String reason,
    required DateTime timestamp,
  }) = ListSubmissionEventCancelled;

  factory ListSubmissionEvent.fromJson(Map<String, dynamic> json) =>
      _$ListSubmissionEventFromJson(json);
}
